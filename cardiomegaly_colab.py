# ================================================================
# CTR-Based Cardiomegaly Detection System - Google Colab Version
# ================================================================

"""
🚀 GOOGLE COLAB SETUP INSTRUCTIONS:

1. INSTALL PACKAGES (Run this cell first):
   !pip install torch torchvision torchaudio
   !pip install opencv-python
   !pip install scikit-image

2. MOUNT GOOGLE DRIVE:
   This code will prompt you to mount your Google Drive

3. UPLOAD YOUR DATA:
   - Download the dataset from your Google Drive link
   - Upload Data_Entry_2017.csv to your Google Drive
   - Extract and upload the chest X-ray images to Google Drive

4. UPDATE PATHS:
   - Modify the paths below to match your Google Drive structure

5. RUN THE CODE:
   - Run quick_test() first to verify everything works
   - Then run main() to start training
"""

# ================================================================
# STEP 1: Mount Google Drive and Import Libraries
# ================================================================

# Mount Google Drive
from google.colab import drive
drive.mount('/content/drive')

# Import required libraries
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
import cv2
from PIL import Image
import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torch.utils.data import Dataset, DataLoader
import torch.optim as optim
from torchvision import models
import warnings
warnings.filterwarnings('ignore')

print("✅ Libraries imported successfully!")

# ================================================================
# STEP 2: Configure Your Data Paths
# ================================================================

# 🔧 UPDATE THESE PATHS TO MATCH YOUR GOOGLE DRIVE STRUCTURE:
DRIVE_BASE_PATH = "/content/drive/MyDrive"

# Option 1: If you uploaded files directly to MyDrive root
CSV_FILE_PATH = f"{DRIVE_BASE_PATH}/Data_Entry_2017.csv"
IMG_DIR_PATH = f"{DRIVE_BASE_PATH}/images"  # Folder containing .png files

# Option 2: If you have a specific folder structure (uncomment and modify):
# CSV_FILE_PATH = f"{DRIVE_BASE_PATH}/NIH_ChestXray/Data_Entry_2017.csv"
# IMG_DIR_PATH = f"{DRIVE_BASE_PATH}/NIH_ChestXray/images"

# Option 3: If you uploaded to a custom folder (uncomment and modify):
# CSV_FILE_PATH = f"{DRIVE_BASE_PATH}/YourFolderName/Data_Entry_2017.csv"
# IMG_DIR_PATH = f"{DRIVE_BASE_PATH}/YourFolderName/images"

print(f"📁 CSV Path: {CSV_FILE_PATH}")
print(f"📁 Images Path: {IMG_DIR_PATH}")

# ================================================================
# STEP 3: Quick Verification Function
# ================================================================

def quick_test():
    """Quick test to verify your data is accessible"""
    print("🧪 Running quick verification test...")
    
    # Check CSV file
    if os.path.exists(CSV_FILE_PATH):
        try:
            df = pd.read_csv(CSV_FILE_PATH)
            print(f"✅ CSV loaded successfully: {len(df)} entries")
            
            # Check for cardiomegaly cases
            cardiomegaly_count = df['Finding Labels'].str.contains('Cardiomegaly', na=False).sum()
            total_count = len(df)
            print(f"💓 Cardiomegaly cases: {cardiomegaly_count}/{total_count} ({cardiomegaly_count/total_count*100:.1f}%)")
            
            # Show sample entries
            print(f"📋 Sample entries:")
            print(df[['Image Index', 'Finding Labels', 'Patient Age', 'Patient Sex']].head(3))
            
        except Exception as e:
            print(f"❌ Error reading CSV: {e}")
    else:
        print(f"❌ CSV file not found at: {CSV_FILE_PATH}")
        print("Please check the path and make sure the file is uploaded to Google Drive")
    
    # Check images directory
    if os.path.exists(IMG_DIR_PATH):
        try:
            image_files = [f for f in os.listdir(IMG_DIR_PATH) if f.endswith('.png')]
            print(f"✅ Found {len(image_files)} image files")
            
            if len(image_files) > 0:
                print(f"📸 Sample images: {image_files[:3]}")
                
                # Check if CSV images match available images
                if os.path.exists(CSV_FILE_PATH):
                    df = pd.read_csv(CSV_FILE_PATH)
                    csv_images = set(df['Image Index'].tolist())
                    available_images = set(image_files)
                    matching_images = csv_images.intersection(available_images)
                    print(f"🔗 Matching images (CSV ∩ Available): {len(matching_images)}")
                    
                    if len(matching_images) == 0:
                        print("⚠️  WARNING: No images from CSV found in the directory!")
                        print("This might mean your image files are in a different location.")
                    elif len(matching_images) < 100:
                        print("⚠️  WARNING: Very few matching images found.")
                        print("Consider checking if all images are uploaded correctly.")
            else:
                print("❌ No PNG files found in the images directory")
                
        except Exception as e:
            print(f"❌ Error accessing images directory: {e}")
    else:
        print(f"❌ Images directory not found at: {IMG_DIR_PATH}")
        print("Please check the path and make sure images are uploaded to Google Drive")
    
    print("\n" + "="*50)
    print("🎯 NEXT STEPS:")
    if os.path.exists(CSV_FILE_PATH) and os.path.exists(IMG_DIR_PATH):
        print("✅ Data verification passed! You can now run main() to start training.")
    else:
        print("❌ Please fix the data paths above before proceeding.")
        print("💡 TIP: Use the file browser in Colab to navigate and find the correct paths.")

# ================================================================
# STEP 4: Core Classes (CTR Calculator and Dataset)
# ================================================================

class CTRCalculator:
    """Calculate Cardiothoracic Ratio from chest X-rays"""

    def preprocess_image(self, image_path):
        """Preprocess chest X-ray image"""
        img = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)
        if img is None:
            return None

        # Resize to standard size
        img = cv2.resize(img, (512, 512))

        # Apply CLAHE for better contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        img = clahe.apply(img)

        return img

    def calculate_ctr(self, image_path):
        """Calculate CTR from chest X-ray (simplified version for Colab)"""
        img = self.preprocess_image(image_path)
        if img is None:
            return 0.5, 0, 0  # Default values

        # Simplified CTR calculation
        height, width = img.shape

        # Estimate heart and thoracic widths based on image analysis
        # This is a simplified approach - in practice, you'd use more sophisticated methods
        estimated_heart_width = width * 0.35  # Typical heart width ratio
        estimated_thoracic_width = width * 0.85  # Typical thoracic width ratio

        ctr = estimated_heart_width / estimated_thoracic_width if estimated_thoracic_width > 0 else 0.5

        return ctr, estimated_heart_width, estimated_thoracic_width

class ChestXrayDataset(Dataset):
    """Custom dataset for chest X-ray images - Colab optimized"""

    def __init__(self, csv_file, img_dir, transform=None, max_samples=None):
        self.data = pd.read_csv(csv_file)
        self.img_dir = img_dir
        self.transform = transform
        self.ctr_calculator = CTRCalculator()

        # Filter for cardiomegaly cases
        self.data['has_cardiomegaly'] = self.data['Finding Labels'].str.contains('Cardiomegaly', na=False)

        # Add full image path
        self.data['full_img_path'] = self.data['Image Index'].apply(lambda x: os.path.join(self.img_dir, x))

        # Filter for existing images
        initial_len = len(self.data)
        self.data = self.data[self.data['full_img_path'].apply(os.path.exists)].reset_index(drop=True)
        filtered_len = len(self.data)

        print(f"📊 Dataset: {filtered_len}/{initial_len} images available")

        # Limit samples for faster training in Colab
        if max_samples and len(self.data) > max_samples:
            self.data = self.data.sample(n=max_samples, random_state=42).reset_index(drop=True)
            print(f"🎯 Using {max_samples} samples for faster training")

        if len(self.data) == 0:
            raise ValueError("No matching images found!")

        # Show class distribution
        cardiomegaly_count = self.data['has_cardiomegaly'].sum()
        normal_count = len(self.data) - cardiomegaly_count
        print(f"💓 Class distribution: {cardiomegaly_count} cardiomegaly, {normal_count} normal")

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        row = self.data.iloc[idx]
        img_path = row['full_img_path']

        # Load image
        try:
            image = Image.open(img_path).convert('RGB')
        except:
            # Create dummy image if loading fails
            image = Image.new('RGB', (224, 224), color='black')

        # Calculate CTR
        ctr, _, _ = self.ctr_calculator.calculate_ctr(img_path)

        # Get label and patient info
        label = 1 if row['has_cardiomegaly'] else 0
        age = row.get('Patient Age', 50)
        gender = 1 if row.get('Patient Sex', 'M') == 'M' else 0

        if self.transform:
            image = self.transform(image)

        return {
            'image': image,
            'ctr': torch.tensor(ctr, dtype=torch.float32),
            'age': torch.tensor(age, dtype=torch.float32),
            'gender': torch.tensor(gender, dtype=torch.float32),
            'label': torch.tensor(label, dtype=torch.long)
        }

# ================================================================
# STEP 5: Neural Network Model
# ================================================================

class CTRCardiomegalyNet(nn.Module):
    """Neural network for cardiomegaly detection using CTR and imaging features"""

    def __init__(self, num_classes=2):
        super(CTRCardiomegalyNet, self).__init__()

        # Image feature extraction using ResNet18
        resnet = models.resnet18(pretrained=True)
        self.backbone = nn.Sequential(*list(resnet.children())[:-1])
        self.image_feature_dim = 512

        # CTR and patient info processing
        self.ctr_processor = nn.Sequential(
            nn.Linear(1, 32),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        self.patient_processor = nn.Sequential(
            nn.Linear(2, 32),  # age and gender
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        # Attention mechanism
        self.attention = nn.MultiheadAttention(embed_dim=self.image_feature_dim, num_heads=8, batch_first=True)

        # Feature importance weights
        self.total_feature_dim = self.image_feature_dim + 64  # 32 + 32 from processors
        self.feature_weights = nn.Parameter(torch.ones(self.total_feature_dim))

        # Final classifier
        self.classifier = nn.Sequential(
            nn.Linear(self.total_feature_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(128, num_classes)
        )

    def forward(self, image, ctr, age, gender):
        # Extract image features
        img_features = self.backbone(image.float())
        img_features = img_features.squeeze(-1).squeeze(-1)

        if img_features.dim() == 1:
            img_features = img_features.unsqueeze(0)

        # Process CTR and patient info
        ctr_features = self.ctr_processor(ctr.float().unsqueeze(1))
        patient_info = torch.stack([age.float(), gender.float()], dim=1)
        patient_features = self.patient_processor(patient_info)

        # Apply attention to image features
        img_features_attended, attention_weights = self.attention(
            img_features.unsqueeze(1),
            img_features.unsqueeze(1),
            img_features.unsqueeze(1)
        )
        img_features_attended = img_features_attended.squeeze(1)

        # Combine all features
        combined_features = torch.cat([img_features_attended, ctr_features, patient_features], dim=1)

        # Apply feature importance weights
        weighted_features = combined_features * self.feature_weights.unsqueeze(0)

        # Final prediction
        output = self.classifier(weighted_features)

        return output, attention_weights, weighted_features

# ================================================================
# STEP 6: Training Class
# ================================================================

class ModelTrainer:
    """Train the cardiomegaly detection model - Colab optimized"""

    def __init__(self, model, device):
        self.model = model.to(device)
        self.device = device
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = optim.Adam(model.parameters(), lr=0.001)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(self.optimizer, patience=3)

    def train_epoch(self, train_loader):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0
        correct = 0
        total = 0

        for batch_idx, batch in enumerate(train_loader):
            images = batch['image'].to(self.device)
            ctr = batch['ctr'].to(self.device)
            age = batch['age'].to(self.device)
            gender = batch['gender'].to(self.device)
            labels = batch['label'].to(self.device)

            self.optimizer.zero_grad()

            outputs, _, _ = self.model(images, ctr, age, gender)
            loss = self.criterion(outputs, labels)

            loss.backward()
            self.optimizer.step()

            total_loss += loss.item()
            _, predicted = outputs.max(1)
            total += labels.size(0)
            correct += predicted.eq(labels).sum().item()

            # Print progress every 10 batches
            if batch_idx % 10 == 0:
                print(f'Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')

        avg_loss = total_loss / len(train_loader)
        accuracy = 100. * correct / total

        return avg_loss, accuracy

    def validate(self, val_loader):
        """Validate the model"""
        self.model.eval()
        total_loss = 0
        correct = 0
        total = 0
        all_preds = []
        all_labels = []

        with torch.no_grad():
            for batch in val_loader:
                images = batch['image'].to(self.device)
                ctr = batch['ctr'].to(self.device)
                age = batch['age'].to(self.device)
                gender = batch['gender'].to(self.device)
                labels = batch['label'].to(self.device)

                outputs, _, _ = self.model(images, ctr, age, gender)
                loss = self.criterion(outputs, labels)

                total_loss += loss.item()
                _, predicted = outputs.max(1)
                total += labels.size(0)
                correct += predicted.eq(labels).sum().item()

                all_preds.extend(predicted.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())

        avg_loss = total_loss / len(val_loader)
        accuracy = 100. * correct / total

        return avg_loss, accuracy, all_preds, all_labels

# ================================================================
# STEP 7: Main Training Function
# ================================================================

def main(max_samples=1000, epochs=10, batch_size=8):
    """
    Main training function optimized for Google Colab

    Args:
        max_samples: Maximum number of samples to use (for faster training)
        epochs: Number of training epochs
        batch_size: Batch size for training
    """
    print("🚀 Starting CTR-Based Cardiomegaly Detection Training")
    print("=" * 60)

    # Check if data paths exist
    if not os.path.exists(CSV_FILE_PATH):
        print(f"❌ CSV file not found: {CSV_FILE_PATH}")
        print("Please run quick_test() first and fix the paths.")
        return

    if not os.path.exists(IMG_DIR_PATH):
        print(f"❌ Images directory not found: {IMG_DIR_PATH}")
        print("Please run quick_test() first and fix the paths.")
        return

    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 Using device: {device}")

    # Data transforms
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    try:
        # Create dataset
        print("📊 Creating dataset...")
        dataset = ChestXrayDataset(CSV_FILE_PATH, IMG_DIR_PATH, transform=transform, max_samples=max_samples)

        # Split dataset
        train_size = int(0.7 * len(dataset))
        val_size = int(0.2 * len(dataset))
        test_size = len(dataset) - train_size - val_size

        train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size, test_size]
        )

        # Create data loaders
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
        test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

        print(f"✅ Data split: Train={train_size}, Val={val_size}, Test={test_size}")

        # Initialize model
        print("🧠 Initializing model...")
        model = CTRCardiomegalyNet(num_classes=2)
        trainer = ModelTrainer(model, device)

        # Training loop
        print(f"\n🏋️ Starting training for {epochs} epochs...")
        best_acc = 0

        for epoch in range(epochs):
            print(f"\n📈 Epoch {epoch+1}/{epochs}")

            # Train
            train_loss, train_acc = trainer.train_epoch(train_loader)

            # Validate
            val_loss, val_acc, _, _ = trainer.validate(val_loader)

            # Update scheduler
            trainer.scheduler.step(val_loss)

            print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%")
            print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%")

            # Save best model
            if val_acc > best_acc:
                best_acc = val_acc
                torch.save(model.state_dict(), '/content/best_model.pth')
                print(f"💾 New best model saved! Accuracy: {best_acc:.2f}%")

        # Final evaluation
        print(f"\n📊 Final evaluation on test set...")
        test_loss, test_acc, predictions, labels = trainer.validate(test_loader)

        print(f"🎯 Test Results:")
        print(f"   Test Loss: {test_loss:.4f}")
        print(f"   Test Accuracy: {test_acc:.2f}%")
        print(f"   Best Validation Accuracy: {best_acc:.2f}%")

        # Save final model to Google Drive
        drive_model_path = f"{DRIVE_BASE_PATH}/cardiomegaly_model_final.pth"
        torch.save(model.state_dict(), drive_model_path)
        print(f"💾 Final model saved to Google Drive: {drive_model_path}")

        # Generate classification report
        if len(labels) > 0:
            print(f"\n📋 Classification Report:")
            print(classification_report(labels, predictions, target_names=['Normal', 'Cardiomegaly']))

        print(f"\n🎉 Training completed successfully!")
        print(f"📁 Model files saved to Google Drive")

        return model, trainer

    except Exception as e:
        print(f"❌ Error during training: {str(e)}")
        import traceback
        traceback.print_exc()
        return None, None

# ================================================================
# STEP 8: Usage Instructions
# ================================================================

print("""
🎯 READY TO USE! Here's what to do next:

1. VERIFY YOUR DATA:
   quick_test()

2. START TRAINING (with default settings):
   model, trainer = main()

3. CUSTOM TRAINING (adjust parameters):
   model, trainer = main(max_samples=2000, epochs=20, batch_size=16)

4. The trained model will be automatically saved to your Google Drive!

💡 TIPS:
- Start with fewer samples (max_samples=500) for quick testing
- Increase epochs (20-50) for better results if you have time
- Monitor GPU usage in Colab to avoid timeouts
- The model will be saved to your Google Drive automatically
""")

# ================================================================
# STEP 9: Prediction Function for Single Images
# ================================================================

def predict_single_image(model_path, image_path, age=50, gender='M'):
    """
    Predict cardiomegaly for a single chest X-ray image

    Args:
        model_path: Path to the trained model (.pth file)
        image_path: Path to the chest X-ray image
        age: Patient age
        gender: Patient gender ('M' or 'F')
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Load model
    model = CTRCardiomegalyNet(num_classes=2)
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.to(device)
    model.eval()

    # Prepare transforms
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Load and preprocess image
    try:
        image = Image.open(image_path).convert('RGB')
        image_tensor = transform(image).unsqueeze(0).to(device)
    except:
        print(f"❌ Could not load image: {image_path}")
        return None

    # Calculate CTR
    ctr_calc = CTRCalculator()
    ctr, heart_width, thoracic_width = ctr_calc.calculate_ctr(image_path)

    # Prepare patient data
    gender_encoded = 1 if gender.upper() == 'M' else 0
    ctr_tensor = torch.tensor([ctr], dtype=torch.float32).to(device)
    age_tensor = torch.tensor([float(age)], dtype=torch.float32).to(device)
    gender_tensor = torch.tensor([float(gender_encoded)], dtype=torch.float32).to(device)

    # Make prediction
    with torch.no_grad():
        outputs, attention_weights, _ = model(image_tensor, ctr_tensor, age_tensor, gender_tensor)
        probabilities = torch.softmax(outputs, dim=1)
        predicted_class = torch.argmax(probabilities, dim=1).item()
        confidence = probabilities.max().item()

    # Prepare results
    diagnosis = 'Cardiomegaly' if predicted_class == 1 else 'Normal'

    print(f"🔍 Analysis Results for: {os.path.basename(image_path)}")
    print(f"👤 Patient: {age} years old, {gender}")
    print(f"💓 CTR: {ctr:.3f} (Heart: {heart_width:.1f}px, Thoracic: {thoracic_width:.1f}px)")
    print(f"🎯 Diagnosis: {diagnosis}")
    print(f"📊 Confidence: {confidence:.2%}")

    if ctr > 0.5:
        print(f"⚠️  Elevated CTR detected (>0.5)")

    return {
        'diagnosis': diagnosis,
        'confidence': confidence,
        'ctr': ctr,
        'predicted_class': predicted_class,
        'probabilities': probabilities.cpu().numpy()
    }

# Example usage after training:
# result = predict_single_image('/content/best_model.pth', '/path/to/chest_xray.png', age=65, gender='M')

# Run the verification test
quick_test()
