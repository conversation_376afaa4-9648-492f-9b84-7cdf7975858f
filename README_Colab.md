# CTR-Based Cardiomegaly Detection System - Google Colab Guide

## 🚀 Quick Start Guide

This system uses **Cardiothoracic Ratio (CTR)** analysis combined with deep learning to detect cardiomegaly in chest X-rays.

### Step 1: Setup Google Colab

1. **Open Google Colab**: Go to [colab.research.google.com](https://colab.research.google.com)
2. **Upload the code**: Upload `cardiomegaly_colab.py` to Colab or copy-paste the code into cells
3. **Enable GPU**: Go to Runtime → Change runtime type → Hardware accelerator → GPU

### Step 2: Install Dependencies

Run this in the first cell:
```python
!pip install torch torchvision torchaudio
!pip install opencv-python
!pip install scikit-image
```

### Step 3: Prepare Your Data

#### Option A: Upload from Google Drive Link
1. Download the dataset from your Google Drive link: `https://drive.google.com/file/d/1tyVoz5NeQ49ZlNz0QQIQZalbr9kIGOTG/view?usp=drive_link`
2. Extract the files to your Google Drive
3. Make sure you have:
   - `Data_Entry_2017.csv` (the metadata file)
   - A folder with `.png` image files

#### Option B: Direct Upload to Colab
1. Upload `Data_Entry_2017.csv` directly to Colab
2. Upload a sample of images for testing

### Step 4: Update Paths in the Code

In the code, update these paths to match your Google Drive structure:

```python
# Update these paths:
CSV_FILE_PATH = "/content/drive/MyDrive/Data_Entry_2017.csv"
IMG_DIR_PATH = "/content/drive/MyDrive/images"  # Folder with .png files
```

### Step 5: Run the Code

1. **Test your setup**:
   ```python
   quick_test()
   ```

2. **Start training** (quick test with 500 samples):
   ```python
   model, trainer = main(max_samples=500, epochs=5)
   ```

3. **Full training** (if you have time and data):
   ```python
   model, trainer = main(max_samples=2000, epochs=20)
   ```

## 📊 Expected Results

- **Training time**: 5-30 minutes depending on data size and epochs
- **Accuracy**: 70-85% (depends on data quality and training time)
- **Output**: Trained model saved to your Google Drive

## 🔍 Making Predictions

After training, you can predict on new images:

```python
# Predict on a single image
result = predict_single_image(
    model_path='/content/best_model.pth',
    image_path='/path/to/chest_xray.png',
    age=65,
    gender='M'
)
```

## 📁 File Structure

Your Google Drive should look like this:
```
/content/drive/MyDrive/
├── Data_Entry_2017.csv
├── images/
│   ├── 00000001_000.png
│   ├── 00000001_001.png
│   └── ... (more .png files)
└── cardiomegaly_model_final.pth (created after training)
```

## ⚠️ Troubleshooting

### Common Issues:

1. **"CSV file not found"**
   - Check the path in `CSV_FILE_PATH`
   - Make sure Google Drive is mounted
   - Verify the file is uploaded correctly

2. **"No matching images found"**
   - Check the path in `IMG_DIR_PATH`
   - Ensure image files are `.png` format
   - Verify image filenames match those in the CSV

3. **"CUDA out of memory"**
   - Reduce `batch_size` (try 4 or 2)
   - Reduce `max_samples` (try 500 or 1000)
   - Restart runtime and try again

4. **Training is slow**
   - Make sure GPU is enabled in Colab
   - Reduce `max_samples` for faster training
   - Use fewer epochs for testing

### Performance Tips:

- **Start small**: Use `max_samples=500` and `epochs=5` for initial testing
- **Monitor resources**: Check GPU/RAM usage in Colab
- **Save frequently**: The model auto-saves to Google Drive
- **Use GPU**: Always enable GPU runtime for faster training

## 🎯 Understanding the Results

The system provides:
- **CTR (Cardiothoracic Ratio)**: Normal < 0.5, Enlarged ≥ 0.5
- **Confidence**: How certain the model is about its prediction
- **Classification**: Normal vs Cardiomegaly

### Example Output:
```
🔍 Analysis Results for: chest_xray.png
👤 Patient: 65 years old, M
💓 CTR: 0.547 (Heart: 180.2px, Thoracic: 329.1px)
🎯 Diagnosis: Cardiomegaly
📊 Confidence: 87.3%
⚠️  Elevated CTR detected (>0.5)
```

## 📚 Next Steps

1. **Improve the model**: Train with more data and epochs
2. **Validate results**: Compare with radiologist assessments
3. **Deploy**: Use the saved model for real-world predictions
4. **Integrate**: Connect with hospital PACS systems

## 🔬 Technical Details

- **Architecture**: ResNet18 backbone + CTR features + patient demographics
- **Input**: 224x224 RGB images + CTR + age + gender
- **Output**: Binary classification (Normal/Cardiomegaly)
- **Training**: Adam optimizer, learning rate scheduling
- **Validation**: 70/20/10 train/val/test split

## 📞 Support

If you encounter issues:
1. Run `quick_test()` to diagnose problems
2. Check the error messages carefully
3. Verify your data paths and file formats
4. Try with smaller datasets first

Good luck with your cardiomegaly detection system! 🏥✨
